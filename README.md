# 手机版八字命理分析系统

一个美观的手机版八字命理分析软件，采用现代化的纯白色简洁设计风格，融合中国传统文化元素，支持公历农历双历法和传统十二时辰选择方式。

## 🌟 功能特色

### 📱 四大核心页面
- **首页**：动态欢迎效果 + 智能信息录入系统
- **分析结果**：命主信息展示 + 九宫格功能按钮
- **知识库**：左侧抽屉导航式设计，丰富的命理知识
- **个人中心**：微信登录 + 个人信息管理

### 🎯 智能录入系统
- **阴阳历法选择**：支持阳历/阴历切换
- **便捷日期选择**：年月日下拉选择器，自动适配月份天数
- **灵活时间输入**：支持具体时间(时分)和传统时辰两种方式
- **示例数据填充**：一键填入1985年8月13日14时40分示例
- **智能表单验证**：实时验证输入完整性

### 🎨 设计亮点
- **简洁首页**：去除复杂动画，突出核心功能
- **纯白背景**：现代简洁的视觉风格
- **精美Logo**：旋转太极图标，体现传统文化
- **紧凑布局**：优化表单排列，提升用户体验
- **响应式设计**：完美适配各种屏幕尺寸

### 🔮 分析功能
- **概述**：整体命理分析
- **强弱**：日主强弱判断
- **喜忌**：喜用神忌神分析
- **性格**：性格特征解读
- **健康**：健康状况提醒
- **六亲**：家庭关系分析
- **婚姻**：感情运势预测
- **财富**：财运状况分析
- **建议**：人生指导建议

### ⏰ 时间选择功能
- **简化历法选择**：小巧的阳历/阴历切换按钮
- **便捷日期输入**：年月日下拉选择器，智能联动
- **十二时辰选择**：传统时辰选择方式，符合命理习惯
- **智能验证**：完整的表单验证和错误提示
- **界面简洁**：删除出生地和示例数据，突出核心功能

## 🛠️ 技术架构

### 前端技术
- **HTML5**：语义化结构
- **CSS3**：现代样式设计
  - Flexbox/Grid布局
  - CSS动画和过渡效果
  - 磨砂玻璃效果（backdrop-filter）
  - 响应式设计
- **JavaScript ES6+**：交互逻辑
  - 模块化设计
  - 事件驱动架构
  - 本地存储管理

### 核心模块
- **BaziApp**：主应用逻辑
- **BaziCalculator**：八字计算引擎
- **UI组件**：页面交互组件

## 📁 项目结构

```
手机版八字排盘分析系统/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   ├── app.js          # 主应用逻辑
│   └── bazi.js         # 八字计算核心
├── images/             # 图片资源
└── README.md           # 项目说明
```

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone [项目地址]
cd 手机版八字排盘分析系统
```

### 2. 启动服务
```bash
# 使用Python
python -m http.server 8000

# 或使用Node.js
npx http-server -p 8000
```

### 3. 访问应用
打开浏览器访问：`http://localhost:8000`

## 📱 使用说明

### 首页操作
1. **基本信息录入**：
   - 输入姓名和选择性别（同一行布局）
   - 选择历法类型（小巧的阳历/阴历切换按钮）
   - 选择出生日期（年/月/日下拉选择器）
   - 选择出生时辰（十二时辰传统选择）

2. **开始分析**：
   - 点击"开始分析"按钮
   - 系统自动验证信息完整性
   - 计算八字并跳转到结果页

### 分析结果
- 查看命主基本信息和八字排盘
- 点击九宫格按钮查看详细分析
- 每个分析项目都有详细的解读说明

### 知识库学习
- 点击左上角菜单按钮打开抽屉导航
- 选择不同分类学习命理知识
- 包含基础知识、天干地支、五行理论等

### 个人中心
- 支持微信登录（模拟）
- 查看分析历史
- 管理个人设置

## 🎯 核心算法

### 八字计算（已修复优化）
- **年柱**：考虑立春节气的准确年份计算，以甲子年（1984年）为基准
- **月柱**：严格按照二十四节气确定月支 + 五虎遁月法推月干
  - 节气划分：立春、惊蛰、清明、立夏、芒种、小暑、立秋、白露、寒露、立冬、大雪、小寒
  - 五虎遁月：甲己丙作首，乙庚戊为头，丙辛庚寅顺，丁壬壬寅真，戊癸甲寅立
- **日柱**：改进的万年历算法 + 真太阳时校正 + 子时特殊处理
  - 万年历基准：1900年1月31日甲子日
  - 真太阳时校正：根据经纬度调整时间（东经120度为基准）
  - 子时处理：夜子时（23:00-00:00）日柱当日时柱次日，早子时（00:00-01:00）日时柱均次日
- **时柱**：五鼠遁时法，根据日干推时干（甲己甲作首，乙庚丙作首...）

### 五行分析
- 统计八字中五行数量
- 计算五行平衡度
- 分析最强和最弱的五行

### 喜忌神判断
- 根据日主强弱确定用神
- 分析相生相克关系
- 给出具体的喜忌建议

## 🌈 设计理念

### 色彩系统
- **金**：金黄色系 (#FFD700)
- **木**：绿色系 (#32CD32)
- **水**：蓝色系 (#1E90FF)
- **火**：红色系 (#FF4500)
- **土**：土黄色系 (#DEB887)

### 视觉效果
- 磨砂玻璃背景
- 柔和的阴影效果
- 流畅的动画过渡
- 传统文化符号点缀

## 📊 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 移动设备
- iOS Safari 12+
- Android Chrome 60+
- 微信内置浏览器

## 🔧 自定义配置

### 修改样式
编辑 `css/style.css` 文件可以自定义：
- 颜色主题
- 布局样式
- 动画效果
- 响应式断点

### 扩展功能
在 `js/app.js` 中可以添加：
- 新的分析模块
- 数据存储功能
- 社交分享功能
- 更多交互特效

## 🔄 更新日志

### v2.2.0 - 日柱万年历算法修复（最新）
- ✅ **万年历算法优化**：以1900年1月31日甲子日为精确基准
- ✅ **真太阳时校正**：根据出生地经纬度调整时间
  - 东经120度为中国标准时间基准
  - 经度每差1度，时间差4分钟
  - 支持全国各地的时间校正
- ✅ **子时特殊处理**：严格按照传统规则
  - 夜子时（23:00-00:00）：日柱用当日，时柱用次日
  - 早子时（00:00-01:00）：日柱和时柱均用次日
- ✅ **节气分界处理**：精确到时分的节气交替判断
- ✅ **调试信息完善**：提供详细的计算过程信息

### v2.1.0 - 月柱节气算法修复
- ✅ **月柱节气算法**：严格按照二十四节气确定月支
  - 正月寅月：立春到惊蛰
  - 二月卯月：惊蛰到清明
  - 三月辰月：清明到立夏
  - 四月巳月：立夏到芒种
  - 五月午月：芒种到小暑
  - 六月未月：小暑到立秋
  - 七月申月：立秋到白露
  - 八月酉月：白露到寒露
  - 九月戌月：寒露到立冬
  - 十月亥月：立冬到大雪
  - 十一月子月：大雪到小寒
  - 十二月丑月：小寒到立春
- ✅ **五虎遁月法**：正确实现年干推月干口诀
  - 甲己之年丙作首，乙庚之岁戊为头
  - 丙辛必定寻庚起，丁壬壬位顺行流
  - 若问戊癸何处起，甲寅之上好追求

### v2.0.0 - 八字排盘逻辑修复
- ✅ **修复年柱计算**：正确考虑立春节气，年份判断更准确
- ✅ **修复日柱计算**：优化万年历算法，确保日期计算准确性
- ✅ **修复时柱计算**：实现正确的五鼠遁时法，根据日干推时干
- ✅ **修复表单问题**：解决年月日选择器无法录入的问题
- ✅ **重新设计阴阳历按钮**：美观的渐变色设计，太阳月亮图标
- ✅ **优化时辰选择**：采用传统十二时辰选择方式

## 📝 开发计划

### 近期计划
- [x] ~~增加更精确的八字计算算法~~ ✅ 已完成
- [ ] 添加大运流年分析功能
- [ ] 实现数据导出功能
- [ ] 优化移动端体验

### 长期规划
- [ ] 集成真实的微信登录
- [ ] 添加用户数据云同步
- [ ] 开发小程序版本
- [ ] 增加AI智能分析

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 贡献方式
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**注意**：本软件仅供娱乐和学习使用，分析结果仅供参考，不应作为人生重大决策的唯一依据。
