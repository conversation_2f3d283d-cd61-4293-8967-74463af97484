/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
    color: #333333;
}

/* 主容器 */
.app-container {
    max-width: 414px;
    margin: 0 auto;
    min-height: 100vh;
    position: relative;
    background: #ffffff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 页面切换 */
.page {
    display: none;
    min-height: calc(100vh - 80px);
    padding: 20px;
    padding-bottom: 100px;
}

.page.active {
    display: block;
}

/* 玻璃卡片效果 */
.glass-card {
    background: #ffffff;
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin-bottom: 20px;
}

/* 首页样式 */
.welcome-section {
    height: 50vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 20px;
    margin-bottom: 30px;
}

.welcome-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 100%);
    border-radius: 20px;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    font-size: 24px;
    color: rgba(102, 126, 234, 0.6);
    animation: float 6s ease-in-out infinite;
}

.element.taiji {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element.star {
    top: 60%;
    right: 15%;
    animation-delay: 1.5s;
}

.element.moon {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

.element.sun {
    top: 40%;
    right: 30%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.welcome-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #333333;
}

.app-title {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-char {
    display: inline-block;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

.title-char:nth-child(1) { animation-delay: 0s; }
.title-char:nth-child(2) { animation-delay: 0.2s; }
.title-char:nth-child(3) { animation-delay: 0.4s; }
.title-char:nth-child(4) { animation-delay: 0.6s; }

@keyframes titleGlow {
    from {
        filter: brightness(1);
        transform: scale(1);
    }
    to {
        filter: brightness(1.2);
        transform: scale(1.05);
    }
}

.app-subtitle {
    font-size: 16px;
    opacity: 0.9;
    letter-spacing: 2px;
}

/* 表单样式 */
.section-title {
    color: #333333;
    font-size: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #333333;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background: #ffffff;
    color: #333333;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-group input::placeholder {
    color: rgba(0, 0, 0, 0.4);
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.gender-selector {
    display: flex;
    gap: 12px;
}

.gender-selector input[type="radio"] {
    display: none;
}

.gender-btn, .calendar-btn {
    flex: 1;
    padding: 12px;
    text-align: center;
    border-radius: 12px;
    background: #f8f9fa;
    color: #666666;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.gender-selector input[type="radio"]:checked + .gender-btn,
.calendar-selector input[type="radio"]:checked + .calendar-btn {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 历法选择样式 */
.calendar-tabs {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
}

.calendar-tabs input[type="radio"] {
    display: none;
}

.calendar-tab {
    flex: 1;
    padding: 12px;
    text-align: center;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px;
}

.calendar-tabs input[type="radio"]:checked + .calendar-tab {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

/* 日期输入容器 */
.date-input-container {
    position: relative;
}

.date-selectors {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.date-select {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
}

.date-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.date-select option {
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

/* 时间输入容器 */
.time-input-container {
    position: relative;
}

.time-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.time-tabs input[type="radio"] {
    display: none;
}

.time-tab {
    flex: 1;
    padding: 10px 12px;
    text-align: center;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 13px;
}

.time-tabs input[type="radio"]:checked + .time-tab {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    border-color: rgba(255, 255, 255, 0.4);
}

.time-input-content {
    position: relative;
}

.time-input-panel {
    display: none;
}

.time-input-panel.active {
    display: block;
}

.time-selectors {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.time-select {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
}

.time-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.time-select option {
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.shichen-select {
    width: 100%;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    margin-bottom: 12px;
}

.shichen-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.shichen-select option {
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

/* 示例提示样式 */
.example-hint {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
}

.example-hint i {
    font-size: 10px;
    opacity: 0.8;
}

/* 示例按钮 */
.example-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.example-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
}

/* 分析按钮样式更新 */
    align-items: center;
    gap: 6px;
    margin-top: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.example-hint i {
    font-size: 10px;
}

/* 历法选择标签 */
.calendar-tabs {
    display: flex;
    gap: 12px;
}

.calendar-tabs input[type="radio"] {
    display: none;
}

.calendar-tab {
    flex: 1;
    padding: 12px;
    text-align: center;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px;
}

.calendar-tabs input[type="radio"]:checked + .calendar-tab {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

/* 日期选择器 */
.date-input-container {
    margin-top: 8px;
}

.date-selectors {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.date-select {
    flex: 1;
    padding: 12px 8px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
}

.date-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.date-select option {
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

/* 时间输入容器 */
.time-input-container {
    margin-top: 8px;
}

.time-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.time-tabs input[type="radio"] {
    display: none;
}

.time-tab {
    flex: 1;
    padding: 10px 8px;
    text-align: center;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 13px;
}

.time-tabs input[type="radio"]:checked + .time-tab {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    border-color: rgba(255, 255, 255, 0.4);
}

.time-input-content {
    position: relative;
}

.time-input-panel {
    display: none;
}

.time-input-panel.active {
    display: block;
}

.time-selectors {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.time-select {
    flex: 1;
    padding: 12px 8px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
}

.time-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.time-select option {
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.shichen-select {
    width: 100%;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    margin-bottom: 8px;
}

.shichen-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.shichen-select option {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px;
}

/* 示例按钮 */
.example-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 16px rgba(79, 172, 254, 0.3);
    margin-bottom: 16px;
}

.example-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.analyze-btn {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 16px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 8px 24px rgba(255, 107, 107, 0.3);
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(255, 107, 107, 0.4);
}

/* 历法选择器样式 */
.calendar-selector {
    display: flex;
    gap: 12px;
}

.calendar-selector input[type="radio"] {
    display: none;
}

/* 历法选择标签页样式 */
.calendar-tabs {
    display: flex;
    gap: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.calendar-tabs input[type="radio"] {
    display: none;
}

.calendar-tab {
    flex: 1;
    padding: 8px 12px;
    text-align: center;
    border-radius: 6px;
    background: transparent;
    color: #666666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.calendar-tabs input[type="radio"]:checked + .calendar-tab {
    background: #667eea;
    color: white;
}

/* 日期输入组样式 */
.date-input-group {
    display: flex;
    gap: 8px;
}

.date-input-group select {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background: #ffffff;
    color: #333333;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 日期输入容器样式 */
.date-input-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.date-selectors {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.date-select {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: #ffffff;
    color: #333333;
    font-size: 16px;
}

/* 时间输入组样式 */
.time-input-group {
    margin-top: 12px;
}

.time-detail-label {
    font-size: 12px;
    color: #666666;
    margin-bottom: 8px;
    display: block;
}

.time-detail {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time-detail select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: #ffffff;
    color: #333333;
    font-size: 14px;
}

.time-detail span {
    color: #666666;
    font-weight: bold;
}

/* 示例提示样式 */
.date-example {
    margin-top: 8px;
}

.date-example small {
    color: #999999;
    font-size: 12px;
}

/* 时辰选择器样式 */
.time-input-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.time-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    background: #ffffff;
    border-radius: 8px;
    padding: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.time-tabs input[type="radio"] {
    display: none;
}

.time-tab {
    flex: 1;
    padding: 8px 12px;
    text-align: center;
    border-radius: 6px;
    background: transparent;
    color: #666666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.time-tabs input[type="radio"]:checked + .time-tab {
    background: #667eea;
    color: white;
}

.time-input-panel {
    display: none;
}

.time-input-panel.active {
    display: block;
}

.time-selectors {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.time-select {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: #ffffff;
    color: #333333;
    font-size: 16px;
}

.shichen-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: #ffffff;
    color: #333333;
    font-size: 16px;
    margin-bottom: 12px;
}

.example-hint {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #999999;
    font-size: 12px;
}

.example-hint i {
    color: #667eea;
}

/* 快速填入按钮 */
.example-btn {
    width: 100%;
    padding: 12px;
    border: 1px dashed #667eea;
    border-radius: 12px;
    background: transparent;
    color: #667eea;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.example-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

/* 分析结果页样式 */
.result-header {
    margin-bottom: 30px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.user-details h3 {
    color: #333333;
    font-size: 18px;
    margin-bottom: 4px;
}

.user-details p {
    color: #666666;
    font-size: 14px;
    margin-bottom: 2px;
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.grid-item {
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333333;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.grid-item:hover {
    transform: translateY(-4px);
    background: #f8f9fa;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.grid-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.grid-item span {
    font-size: 14px;
    font-weight: 500;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    height: 80px;
    background: #ffffff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.08);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    color: #999999;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 12px;
}

.nav-item.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.nav-item i {
    font-size: 20px;
}

.nav-item span {
    font-size: 12px;
}

/* 知识库页面样式 */
.knowledge-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px 0;
}

.drawer-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    padding: 12px;
    color: white;
    font-size: 18px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.knowledge-header h2 {
    color: #333333;
    font-size: 24px;
    font-weight: bold;
}

.knowledge-drawer {
    position: fixed;
    top: 0;
    left: -300px;
    width: 280px;
    height: 100vh;
    background: #ffffff;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease;
    z-index: 2000;
    box-shadow: 4px 0 16px rgba(0, 0, 0, 0.1);
}

.knowledge-drawer.open {
    left: 0;
}

.drawer-content {
    padding: 20px;
    height: 100%;
}

.drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.drawer-header h3 {
    color: #333333;
    font-size: 20px;
}

.drawer-close {
    background: none;
    border: none;
    color: #666666;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.drawer-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.knowledge-nav {
    list-style: none;
}

.knowledge-nav li {
    margin-bottom: 12px;
}

.knowledge-nav a {
    display: block;
    padding: 16px 20px;
    color: #666666;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.knowledge-nav a:hover {
    background: #667eea;
    color: white;
    transform: translateX(8px);
}

.knowledge-content {
    transition: margin-left 0.3s ease;
}

.knowledge-content h3 {
    color: #333333;
    font-size: 20px;
    margin-bottom: 16px;
}

.knowledge-content p {
    color: #666666;
    line-height: 1.6;
    font-size: 16px;
}

/* 个人中心样式 */
.profile-header {
    margin-bottom: 30px;
}

.profile-card {
    text-align: center;
    padding: 40px 24px;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    margin: 0 auto 20px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.wechat-login-btn {
    background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
    border: none;
    border-radius: 16px;
    padding: 16px 32px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto;
    box-shadow: 0 8px 24px rgba(7, 193, 96, 0.3);
    transition: all 0.3s ease;
}

.wechat-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(7, 193, 96, 0.4);
}

.profile-menu {
    background: #ffffff;
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    color: #333333;
    cursor: pointer;
    transition: background 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: #f8f9fa;
}

.menu-item i:first-child {
    font-size: 20px;
    margin-right: 16px;
    width: 24px;
    text-align: center;
}

.menu-item span {
    flex: 1;
    font-size: 16px;
}

.menu-item i:last-child {
    font-size: 14px;
    opacity: 0.6;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 3000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: #ffffff;
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 360px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header h3 {
    color: #333333;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    color: #666666;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modal-body {
    padding: 24px;
    color: #333333;
    line-height: 1.6;
    max-height: 60vh;
    overflow-y: auto;
}

.analysis-detail {
    font-size: 16px;
}

.analysis-detail p {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.detail-tips {
    margin-top: 20px;
    padding: 16px;
    background: #f0f2f5;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-tips h4 {
    color: #333333;
    font-size: 14px;
    margin-bottom: 12px;
}

.detail-tips ul {
    list-style: none;
    padding: 0;
}

.detail-tips li {
    padding: 4px 0;
    font-size: 13px;
    color: #666666;
    position: relative;
    padding-left: 16px;
}

.detail-tips li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #667eea;
}

/* 五行颜色系统 */
.wuxing-jin { color: #FFD700; background: linear-gradient(135deg, #FFD700, #FFA500); }
.wuxing-mu { color: #32CD32; background: linear-gradient(135deg, #32CD32, #228B22); }
.wuxing-shui { color: #1E90FF; background: linear-gradient(135deg, #1E90FF, #0066CC); }
.wuxing-huo { color: #FF4500; background: linear-gradient(135deg, #FF4500, #DC143C); }
.wuxing-tu { color: #DEB887; background: linear-gradient(135deg, #DEB887, #CD853F); }

/* 五行标签 */
.wuxing-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    margin: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 八字显示增强 */
.bazi-display {
    display: flex;
    justify-content: space-between;
    margin: 16px 0;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.bazi-pillar {
    text-align: center;
    flex: 1;
}

.bazi-pillar .pillar-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 8px;
}

.bazi-pillar .pillar-chars {
    font-size: 18px;
    font-weight: bold;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 知识库内容增强 */
.knowledge-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.knowledge-card h4 {
    color: #333333;
    font-size: 18px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.knowledge-card p {
    color: #666666;
    line-height: 1.6;
    margin-bottom: 12px;
}

.knowledge-card .example {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 3px solid #667eea;
    margin-top: 12px;
}

.knowledge-card .example-title {
    font-size: 14px;
    color: #333333;
    margin-bottom: 8px;
    font-weight: bold;
}

.knowledge-card .example-content {
    font-size: 13px;
    color: #666666;
    line-height: 1.5;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-size: 16px;
    margin-top: 20px;
    text-align: center;
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    z-index: 9999;
    font-size: 14px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: toastFadeIn 0.3s ease;
}

@keyframes toastFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 414px) {
    .app-container {
        max-width: 100%;
    }

    .page {
        padding: 16px;
    }

    .app-title {
        font-size: 36px;
    }

    .knowledge-drawer {
        width: 260px;
        left: -260px;
    }
}
