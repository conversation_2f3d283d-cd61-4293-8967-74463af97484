<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字命理分析系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 首页 -->
        <div class="page active" id="home-page">
            <!-- 动态欢迎区域 -->
            <div class="welcome-section">
                <div class="welcome-bg">
                    <div class="floating-elements">
                        <div class="element taiji">☯</div>
                        <div class="element star">✦</div>
                        <div class="element moon">☾</div>
                        <div class="element sun">☀</div>
                    </div>
                </div>
                <div class="welcome-content">
                    <h1 class="app-title">
                        <span class="title-char">八</span>
                        <span class="title-char">字</span>
                        <span class="title-char">命</span>
                        <span class="title-char">理</span>
                    </h1>
                    <p class="app-subtitle">探索命运奥秘，洞察人生智慧</p>
                </div>
            </div>

            <!-- 信息录入区域 -->
            <div class="input-section">
                <div class="glass-card">
                    <h3 class="section-title">
                        <i class="fas fa-user-circle"></i>
                        命主信息
                    </h3>
                    <form class="bazi-form" id="baziForm">
                        <div class="form-group">
                            <label>姓名</label>
                            <input type="text" id="name" placeholder="请输入姓名" required>
                            <div class="example-hint">
                                <i class="fas fa-lightbulb"></i>
                                <span>示例：张三</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>性别</label>
                            <div class="gender-selector">
                                <input type="radio" id="male" name="gender" value="male" checked>
                                <label for="male" class="gender-btn">
                                    <i class="fas fa-mars"></i>男
                                </label>
                                <input type="radio" id="female" name="gender" value="female">
                                <label for="female" class="gender-btn">
                                    <i class="fas fa-venus"></i>女
                                </label>
                            </div>
                        </div>

                        <!-- 历法选择 -->
                        <div class="form-group">
                            <label>历法类型</label>
                            <div class="calendar-tabs">
                                <input type="radio" id="solar" name="calendarType" value="solar" checked>
                                <label for="solar" class="calendar-tab">
                                    <i class="fas fa-sun"></i>阳历
                                </label>
                                <input type="radio" id="lunar" name="calendarType" value="lunar">
                                <label for="lunar" class="calendar-tab">
                                    <i class="fas fa-moon"></i>阴历
                                </label>
                            </div>
                        </div>

                        <!-- 出生日期 -->
                        <div class="form-group">
                            <label id="dateLabel">出生日期（阳历）</label>
                            <div class="date-input-container">
                                <div class="date-selectors" id="dateSelectors">
                                    <select id="birthYear" class="date-select" required>
                                        <option value="">年</option>
                                    </select>
                                    <select id="birthMonth" class="date-select" required>
                                        <option value="">月</option>
                                    </select>
                                    <select id="birthDay" class="date-select" required>
                                        <option value="">日</option>
                                    </select>
                                </div>
                                <div class="example-hint">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>示例：1985年8月13日</span>
                                </div>
                            </div>
                        </div>

                        <!-- 出生时辰 -->
                        <div class="form-group">
                            <label>出生时辰</label>
                            <div class="time-input-container">
                                <div class="time-tabs" id="timeInputMethod">
                                    <input type="radio" id="timeMethod" name="timeInputType" value="time" checked>
                                    <label for="timeMethod" class="time-tab">
                                        <i class="fas fa-clock"></i>具体时间
                                    </label>
                                    <input type="radio" id="shichenMethod" name="timeInputType" value="shichen">
                                    <label for="shichenMethod" class="time-tab">
                                        <i class="fas fa-yin-yang"></i>传统时辰
                                    </label>
                                </div>

                                <div class="time-input-content">
                                    <!-- 具体时间输入 -->
                                    <div class="time-input-panel active" id="timePanel">
                                        <div class="time-selectors">
                                            <select id="birthHour" class="time-select" required>
                                                <option value="">时</option>
                                            </select>
                                            <select id="birthMinute" class="time-select" required>
                                                <option value="">分</option>
                                            </select>
                                        </div>
                                        <div class="example-hint">
                                            <i class="fas fa-lightbulb"></i>
                                            <span>示例：14时40分</span>
                                        </div>
                                    </div>

                                    <!-- 传统时辰输入 -->
                                    <div class="time-input-panel" id="shichenPanel">
                                        <select id="shichenSelect" class="shichen-select">
                                            <option value="">请选择时辰</option>
                                            <option value="zi">子时 (23:00-01:00)</option>
                                            <option value="chou">丑时 (01:00-03:00)</option>
                                            <option value="yin">寅时 (03:00-05:00)</option>
                                            <option value="mao">卯时 (05:00-07:00)</option>
                                            <option value="chen">辰时 (07:00-09:00)</option>
                                            <option value="si">巳时 (09:00-11:00)</option>
                                            <option value="wu">午时 (11:00-13:00)</option>
                                            <option value="wei">未时 (13:00-15:00)</option>
                                            <option value="shen">申时 (15:00-17:00)</option>
                                            <option value="you">酉时 (17:00-19:00)</option>
                                            <option value="xu">戌时 (19:00-21:00)</option>
                                            <option value="hai">亥时 (21:00-23:00)</option>
                                        </select>
                                        <div class="example-hint">
                                            <i class="fas fa-lightbulb"></i>
                                            <span>示例：未时（下午1-3点）</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>出生地点</label>
                            <input type="text" id="birthPlace" placeholder="请输入出生地点">
                            <div class="example-hint">
                                <i class="fas fa-lightbulb"></i>
                                <span>示例：北京市</span>
                            </div>
                        </div>

                        <!-- 快速填入示例按钮 -->
                        <div class="form-group">
                            <button type="button" class="example-btn" id="fillExampleBtn">
                                <i class="fas fa-magic"></i>
                                填入示例数据
                            </button>
                        </div>

                        <button type="submit" class="analyze-btn">
                            <i class="fas fa-search"></i>
                            开始分析
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 分析结果页 -->
        <div class="page" id="result-page">
            <div class="result-header">
                <div class="glass-card user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <h3 id="userName">张三</h3>
                        <p id="userBirth">1990年5月15日 14:30</p>
                        <p id="userBazi">庚午年 辛巳月 甲子日 辛未时</p>
                    </div>
                </div>
            </div>

            <div class="result-grid">
                <div class="grid-item" data-type="overview">
                    <div class="grid-icon">🔮</div>
                    <span>概述</span>
                </div>
                <div class="grid-item" data-type="strength">
                    <div class="grid-icon">💪</div>
                    <span>强弱</span>
                </div>
                <div class="grid-item" data-type="preference">
                    <div class="grid-icon">⚖️</div>
                    <span>喜忌</span>
                </div>
                <div class="grid-item" data-type="personality">
                    <div class="grid-icon">🎭</div>
                    <span>性格</span>
                </div>
                <div class="grid-item" data-type="health">
                    <div class="grid-icon">🏥</div>
                    <span>健康</span>
                </div>
                <div class="grid-item" data-type="family">
                    <div class="grid-icon">👨‍👩‍👧‍👦</div>
                    <span>六亲</span>
                </div>
                <div class="grid-item" data-type="marriage">
                    <div class="grid-icon">💕</div>
                    <span>婚姻</span>
                </div>
                <div class="grid-item" data-type="wealth">
                    <div class="grid-icon">💰</div>
                    <span>财富</span>
                </div>
                <div class="grid-item" data-type="advice">
                    <div class="grid-icon">💡</div>
                    <span>建议</span>
                </div>
            </div>
        </div>

        <!-- 知识库页 -->
        <div class="page" id="knowledge-page">
            <div class="knowledge-header">
                <button class="drawer-toggle" id="drawerToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h2>知识库</h2>
            </div>

            <div class="knowledge-drawer" id="knowledgeDrawer">
                <div class="drawer-content">
                    <div class="drawer-header">
                        <h3>分类导航</h3>
                        <button class="drawer-close" id="drawerClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <ul class="knowledge-nav">
                        <li><a href="#" data-category="basics">基础知识</a></li>
                        <li><a href="#" data-category="tiangan">天干地支</a></li>
                        <li><a href="#" data-category="wuxing">五行理论</a></li>
                        <li><a href="#" data-category="shishen">十神分析</a></li>
                        <li><a href="#" data-category="dayun">大运流年</a></li>
                        <li><a href="#" data-category="cases">经典案例</a></li>
                    </ul>
                </div>
            </div>

            <div class="knowledge-content" id="knowledgeContent">
                <div class="glass-card">
                    <h3>八字命理基础</h3>
                    <p>八字命理学是中国传统文化的重要组成部分，通过分析一个人出生时的年、月、日、时四柱八字，来推断其性格特征、运势走向等...</p>
                </div>
            </div>
        </div>

        <!-- 个人中心页 -->
        <div class="page" id="profile-page">
            <div class="profile-header">
                <div class="glass-card profile-card">
                    <div class="profile-avatar" id="profileAvatar">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div class="profile-info" id="profileInfo">
                        <button class="wechat-login-btn" id="wechatLogin">
                            <i class="fab fa-weixin"></i>
                            微信登录
                        </button>
                    </div>
                </div>
            </div>

            <div class="profile-menu">
                <div class="menu-item">
                    <i class="fas fa-history"></i>
                    <span>分析历史</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="menu-item">
                    <i class="fas fa-star"></i>
                    <span>我的收藏</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="menu-item">
                    <i class="fas fa-question-circle"></i>
                    <span>帮助与反馈</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <nav class="bottom-nav">
            <div class="nav-item active" data-page="home-page">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="nav-item" data-page="result-page">
                <i class="fas fa-chart-line"></i>
                <span>分析结果</span>
            </div>
            <div class="nav-item" data-page="knowledge-page">
                <i class="fas fa-book"></i>
                <span>知识库</span>
            </div>
            <div class="nav-item" data-page="profile-page">
                <i class="fas fa-user"></i>
                <span>个人中心</span>
            </div>
        </nav>
    </div>

    <!-- 详情弹窗 -->
    <div class="modal" id="detailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">详情</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loadingText">正在分析中...</div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/bazi.js"></script>
</body>
</html>
