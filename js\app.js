// 应用主逻辑
class BaziApp {
    constructor() {
        this.currentPage = 'home-page';
        this.isLoggedIn = false;
        this.userInfo = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeApp();
    }

    bindEvents() {
        // 底部导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const targetPage = e.currentTarget.dataset.page;
                this.switchPage(targetPage);
            });
        });

        // 表单提交
        document.getElementById('baziForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });

        // 九宫格按钮点击
        document.querySelectorAll('.grid-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const type = e.currentTarget.dataset.type;
                this.showAnalysisDetail(type);
            });
        });

        // 知识库抽屉
        document.getElementById('drawerToggle').addEventListener('click', () => {
            this.toggleKnowledgeDrawer();
        });

        document.getElementById('drawerClose').addEventListener('click', () => {
            this.closeKnowledgeDrawer();
        });

        // 知识库导航
        document.querySelectorAll('.knowledge-nav a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const category = e.currentTarget.dataset.category;
                this.loadKnowledgeContent(category);
            });
        });

        // 微信登录
        document.getElementById('wechatLogin').addEventListener('click', () => {
            this.handleWechatLogin();
        });

        // 弹窗关闭
        document.getElementById('modalClose').addEventListener('click', () => {
            this.closeModal();
        });

        // 点击弹窗背景关闭
        document.getElementById('detailModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        });
    }

    initializeApp() {
        // 检查本地存储的用户信息
        const savedUser = localStorage.getItem('baziUser');
        if (savedUser) {
            this.userInfo = JSON.parse(savedUser);
            this.isLoggedIn = true;
            this.updateProfileUI();
        }

        // 初始化页面
        this.switchPage('home-page');
    }

    switchPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        document.getElementById(pageId).classList.add('active');

        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-page="${pageId}"]`).classList.add('active');

        this.currentPage = pageId;
    }

    handleFormSubmit() {
        const formData = {
            name: document.getElementById('name').value,
            gender: document.querySelector('input[name="gender"]:checked').value,
            birthDate: document.getElementById('birthDate').value,
            birthTime: document.getElementById('birthTime').value,
            birthPlace: document.getElementById('birthPlace').value
        };

        // 验证表单
        if (!this.validateForm(formData)) {
            return;
        }

        // 显示加载动画
        this.showLoading();

        // 模拟分析过程
        setTimeout(() => {
            this.hideLoading();
            this.processAnalysis(formData);
            this.switchPage('result-page');
        }, 2000);
    }

    validateForm(data) {
        if (!data.name.trim()) {
            this.showToast('请输入姓名');
            return false;
        }
        if (!data.birthDate) {
            this.showToast('请选择出生日期');
            return false;
        }
        if (!data.birthTime) {
            this.showToast('请选择出生时间');
            return false;
        }
        return true;
    }

    processAnalysis(formData) {
        // 更新用户信息显示
        document.getElementById('userName').textContent = formData.name;
        document.getElementById('userBirth').textContent =
            `${formData.birthDate} ${formData.birthTime}`;

        // 这里应该调用八字计算逻辑
        const baziResult = this.calculateBazi(formData);
        document.getElementById('userBazi').textContent = baziResult.bazi;

        // 保存分析结果
        this.currentAnalysis = {
            userInfo: formData,
            result: baziResult
        };
    }

    calculateBazi(formData) {
        // 使用专业的八字计算器
        if (window.baziCalculator) {
            const fullAnalysis = window.baziCalculator.generateFullAnalysis(
                formData.birthDate,
                formData.birthTime,
                formData.name,
                formData.gender
            );

            return {
                bazi: fullAnalysis.bazi.baziString,
                analysis: fullAnalysis.detailedAnalysis,
                fullData: fullAnalysis
            };
        } else {
            // 备用简化计算
            const sampleBazi = [
                '甲子年 丙寅月 戊辰日 庚申时',
                '乙丑年 丁卯月 己巳日 辛酉时',
                '丙寅年 戊辰月 庚午日 壬戌时',
                '丁卯年 己巳月 辛未日 癸亥时'
            ];

            return {
                bazi: sampleBazi[Math.floor(Math.random() * sampleBazi.length)],
                analysis: this.generateAnalysis()
            };
        }
    }

    generateAnalysis() {
        return {
            overview: '命主五行平衡，天赋聪颖，具有很强的学习能力和适应能力。',
            strength: '日主偏强，自立能力强，有主见，不易受他人影响。',
            preference: '喜用神为水木，忌神为火土。宜从事与水木相关的行业。',
            personality: '性格温和，待人真诚，有责任心，但有时过于谨慎。',
            health: '整体健康状况良好，需注意脾胃和心血管方面的保养。',
            family: '与父母关系和睦，兄弟姐妹互助，家庭和谐。',
            marriage: '感情运势较好，适合晚婚，婚后生活幸福美满。',
            wealth: '财运平稳，适合稳健投资，不宜投机取巧。',
            advice: '保持积极心态，多学习充实自己，注意身体健康。'
        };
    }

    showAnalysisDetail(type) {
        const analysis = this.currentAnalysis?.result?.analysis;
        if (!analysis) {
            this.showToast('请先进行八字分析');
            return;
        }

        const titles = {
            overview: '命理概述',
            strength: '日主强弱',
            preference: '喜忌分析',
            personality: '性格特征',
            health: '健康状况',
            family: '六亲关系',
            marriage: '婚姻感情',
            wealth: '财富运势',
            advice: '人生建议'
        };

        document.getElementById('modalTitle').textContent = titles[type];
        document.getElementById('modalBody').innerHTML = `
            <div class="analysis-detail">
                <p>${analysis[type]}</p>
                <div class="detail-tips">
                    <h4>详细解读：</h4>
                    <ul>
                        <li>此分析基于传统八字命理学理论</li>
                        <li>仅供参考，不可完全依赖</li>
                        <li>人生需要自己努力创造</li>
                    </ul>
                </div>
            </div>
        `;
        this.showModal();
    }

    toggleKnowledgeDrawer() {
        const drawer = document.getElementById('knowledgeDrawer');
        drawer.classList.toggle('open');
    }

    closeKnowledgeDrawer() {
        document.getElementById('knowledgeDrawer').classList.remove('open');
    }

    loadKnowledgeContent(category) {
        const content = this.getKnowledgeContent(category);
        document.getElementById('knowledgeContent').innerHTML = `
            <div class="glass-card">
                <h3>${content.title}</h3>
                <p>${content.description}</p>
                <div class="knowledge-details">
                    ${content.details}
                </div>
            </div>
        `;
        this.closeKnowledgeDrawer();
    }

    getKnowledgeContent(category) {
        const contents = {
            basics: {
                title: '八字命理基础',
                description: '八字命理学是中国传统文化的重要组成部分，通过分析一个人出生时的年、月、日、时四柱八字，来推断其性格特征、运势走向等。',
                details: `
                    <div class="knowledge-card">
                        <h4>🔮 什么是八字</h4>
                        <p>八字又称四柱，是指一个人出生时的年、月、日、时，每柱由天干地支组成，共八个字，故称八字。</p>
                        <div class="example">
                            <div class="example-title">示例：</div>
                            <div class="example-content">
                                年柱：甲子（天干甲+地支子）<br>
                                月柱：丙寅（天干丙+地支寅）<br>
                                日柱：戊辰（天干戊+地支辰）<br>
                                时柱：庚申（天干庚+地支申）
                            </div>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h4>📊 分析要素</h4>
                        <p>八字分析主要包括：日主强弱、五行平衡、喜忌神、十神关系、大运流年等。</p>
                    </div>
                `
            },
            tiangan: {
                title: '天干地支',
                description: '天干地支是中国古代的一种纪年、纪月、纪日、纪时的方法，是八字命理的基础。',
                details: `
                    <div class="knowledge-card">
                        <h4>☀️ 十天干</h4>
                        <p>甲、乙、丙、丁、戊、己、庚、辛、壬、癸</p>
                        <div class="example">
                            <div class="example-title">五行属性：</div>
                            <div class="example-content">
                                <span class="wuxing-tag wuxing-mu">甲乙木</span>
                                <span class="wuxing-tag wuxing-huo">丙丁火</span>
                                <span class="wuxing-tag wuxing-tu">戊己土</span>
                                <span class="wuxing-tag wuxing-jin">庚辛金</span>
                                <span class="wuxing-tag wuxing-shui">壬癸水</span>
                            </div>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h4>🌙 十二地支</h4>
                        <p>子、丑、寅、卯、辰、巳、午、未、申、酉、戌、亥</p>
                        <div class="example">
                            <div class="example-title">对应时辰：</div>
                            <div class="example-content">
                                子时(23-1点)、丑时(1-3点)、寅时(3-5点)、卯时(5-7点)<br>
                                辰时(7-9点)、巳时(9-11点)、午时(11-13点)、未时(13-15点)<br>
                                申时(15-17点)、酉时(17-19点)、戌时(19-21点)、亥时(21-23点)
                            </div>
                        </div>
                    </div>
                `
            },
            wuxing: {
                title: '五行理论',
                description: '五行学说是中国古代的一种哲学思想，认为宇宙万物都由金、木、水、火、土五种基本元素构成。',
                details: `
                    <div class="knowledge-card">
                        <h4>🔄 五行相生</h4>
                        <p>金生水、水生木、木生火、火生土、土生金</p>
                        <div class="example">
                            <div class="example-title">相生关系：</div>
                            <div class="example-content">
                                <span class="wuxing-tag wuxing-jin">金</span> →
                                <span class="wuxing-tag wuxing-shui">水</span> →
                                <span class="wuxing-tag wuxing-mu">木</span> →
                                <span class="wuxing-tag wuxing-huo">火</span> →
                                <span class="wuxing-tag wuxing-tu">土</span> →
                                <span class="wuxing-tag wuxing-jin">金</span>
                            </div>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h4>⚔️ 五行相克</h4>
                        <p>金克木、木克土、土克水、水克火、火克金</p>
                        <div class="example">
                            <div class="example-title">相克关系：</div>
                            <div class="example-content">
                                <span class="wuxing-tag wuxing-jin">金</span> 克 <span class="wuxing-tag wuxing-mu">木</span>，
                                <span class="wuxing-tag wuxing-mu">木</span> 克 <span class="wuxing-tag wuxing-tu">土</span>，
                                <span class="wuxing-tag wuxing-tu">土</span> 克 <span class="wuxing-tag wuxing-shui">水</span>，<br>
                                <span class="wuxing-tag wuxing-shui">水</span> 克 <span class="wuxing-tag wuxing-huo">火</span>，
                                <span class="wuxing-tag wuxing-huo">火</span> 克 <span class="wuxing-tag wuxing-jin">金</span>
                            </div>
                        </div>
                    </div>
                `
            },
            shishen: {
                title: '十神分析',
                description: '十神是八字命理中的重要概念，通过日干与其他干支的关系来判断人的性格和运势。',
                details: `
                    <div class="knowledge-card">
                        <h4>👑 十神分类</h4>
                        <p>比肩、劫财、食神、伤官、偏财、正财、七杀、正官、偏印、正印</p>
                        <div class="example">
                            <div class="example-title">十神含义：</div>
                            <div class="example-content">
                                <strong>正官</strong>：代表权威、地位、责任心<br>
                                <strong>偏财</strong>：代表意外之财、投资理财<br>
                                <strong>食神</strong>：代表才华、表达能力、享受<br>
                                <strong>正印</strong>：代表学识、贵人、母亲
                            </div>
                        </div>
                    </div>
                `
            },
            dayun: {
                title: '大运流年',
                description: '大运和流年是预测人生运势变化的重要方法，大运管十年，流年管一年。',
                details: `
                    <div class="knowledge-card">
                        <h4>🔮 大运推算</h4>
                        <p>大运从月柱开始推算，男命阳年生顺行，阴年生逆行；女命相反。</p>
                        <div class="example">
                            <div class="example-title">起运年龄：</div>
                            <div class="example-content">
                                根据出生日到下一个节气的天数计算，一般在3-10岁之间起运。
                            </div>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h4>📅 流年分析</h4>
                        <p>流年是指每一年的天干地支，与命局和大运产生作用，影响当年运势。</p>
                    </div>
                `
            },
            cases: {
                title: '经典案例',
                description: '通过实际案例来学习八字分析的方法和技巧。',
                details: `
                    <div class="knowledge-card">
                        <h4>📖 案例一：富贵命格</h4>
                        <p>某男命：甲子年 丙寅月 戊辰日 庚申时</p>
                        <div class="example">
                            <div class="example-title">分析要点：</div>
                            <div class="example-content">
                                日主戊土生于寅月，木旺土弱，喜火土帮身。<br>
                                年干甲木为正官，月干丙火为正印，形成官印相生格局。<br>
                                时干庚金为食神，泄秀有情，主聪明才智。
                            </div>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h4>💡 学习要点</h4>
                        <p>分析八字要综合考虑：格局、用神、十神、五行平衡等多个因素。</p>
                    </div>
                `
            }
        };

        return contents[category] || contents.basics;
    }

    handleWechatLogin() {
        // 模拟微信登录
        this.showLoading('正在连接微信...');

        setTimeout(() => {
            this.hideLoading();
            this.isLoggedIn = true;
            this.userInfo = {
                name: '微信用户',
                avatar: 'https://via.placeholder.com/80',
                id: 'wx_' + Date.now()
            };

            localStorage.setItem('baziUser', JSON.stringify(this.userInfo));
            this.updateProfileUI();
            this.showToast('登录成功');
        }, 1500);
    }

    updateProfileUI() {
        const profileInfo = document.getElementById('profileInfo');
        if (this.isLoggedIn && this.userInfo) {
            profileInfo.innerHTML = `
                <h3>${this.userInfo.name}</h3>
                <p>已登录</p>
            `;
        }
    }

    showModal() {
        document.getElementById('detailModal').classList.add('show');
    }

    closeModal() {
        document.getElementById('detailModal').classList.remove('show');
    }

    showLoading(text = '正在分析中...') {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingOverlay').classList.add('show');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }

    showToast(message) {
        // 创建简单的提示
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 9999;
            font-size: 14px;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new BaziApp();
});
