// 八字命理计算核心逻辑
class BaziCalculator {
    constructor() {
        this.tian<PERSON>an = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        this.diZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
        this.wuXing = {
            '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
            '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
            '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
            '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
            '戌': '土', '亥': '水'
        };
        this.shi<PERSON><PERSON> = [
            { name: '子时', start: 23, end: 1 },
            { name: '丑时', start: 1, end: 3 },
            { name: '寅时', start: 3, end: 5 },
            { name: '卯时', start: 5, end: 7 },
            { name: '辰时', start: 7, end: 9 },
            { name: '巳时', start: 9, end: 11 },
            { name: '午时', start: 11, end: 13 },
            { name: '未时', start: 13, end: 15 },
            { name: '申时', start: 15, end: 17 },
            { name: '酉时', start: 17, end: 19 },
            { name: '戌时', start: 19, end: 21 },
            { name: '亥时', start: 21, end: 23 }
        ];
    }

    // 计算八字
    calculateBazi(birthDate, birthTime) {
        const date = new Date(birthDate + 'T' + birthTime);

        // 计算年柱
        const yearPillar = this.getYearPillar(date.getFullYear());

        // 计算月柱
        const monthPillar = this.getMonthPillar(date.getFullYear(), date.getMonth() + 1);

        // 计算日柱
        const dayPillar = this.getDayPillar(date);

        // 计算时柱
        const hourPillar = this.getHourPillar(date.getHours(), dayPillar.tianGan);

        return {
            year: yearPillar,
            month: monthPillar,
            day: dayPillar,
            hour: hourPillar,
            baziString: `${yearPillar.tianGan}${yearPillar.diZhi}年 ${monthPillar.tianGan}${monthPillar.diZhi}月 ${dayPillar.tianGan}${dayPillar.diZhi}日 ${hourPillar.tianGan}${hourPillar.diZhi}时`
        };
    }

    // 获取年柱
    getYearPillar(year) {
        // 简化计算，实际应该考虑立春节气
        const tianGanIndex = (year - 4) % 10;
        const diZhiIndex = (year - 4) % 12;

        return {
            tianGan: this.tianGan[tianGanIndex],
            diZhi: this.diZhi[diZhiIndex],
            wuXing: {
                tianGan: this.wuXing[this.tianGan[tianGanIndex]],
                diZhi: this.wuXing[this.diZhi[diZhiIndex]]
            }
        };
    }

    // 获取月柱
    getMonthPillar(year, month) {
        // 简化计算，实际应该根据节气确定
        const yearTianGan = (year - 4) % 10;
        let monthTianGanIndex;

        // 根据年干推月干
        if (yearTianGan === 0 || yearTianGan === 5) { // 甲己年
            monthTianGanIndex = (month + 1) % 10;
        } else if (yearTianGan === 1 || yearTianGan === 6) { // 乙庚年
            monthTianGanIndex = (month + 3) % 10;
        } else if (yearTianGan === 2 || yearTianGan === 7) { // 丙辛年
            monthTianGanIndex = (month + 5) % 10;
        } else if (yearTianGan === 3 || yearTianGan === 8) { // 丁壬年
            monthTianGanIndex = (month + 7) % 10;
        } else { // 戊癸年
            monthTianGanIndex = (month + 9) % 10;
        }

        const monthDiZhiIndex = (month + 1) % 12;

        return {
            tianGan: this.tianGan[monthTianGanIndex],
            diZhi: this.diZhi[monthDiZhiIndex],
            wuXing: {
                tianGan: this.wuXing[this.tianGan[monthTianGanIndex]],
                diZhi: this.wuXing[this.diZhi[monthDiZhiIndex]]
            }
        };
    }

    // 获取日柱
    getDayPillar(date) {
        // 使用简化的万年历算法
        const baseDate = new Date('1900-01-31'); // 庚子日
        const diffDays = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));

        const tianGanIndex = diffDays % 10;
        const diZhiIndex = diffDays % 12;

        return {
            tianGan: this.tianGan[tianGanIndex],
            diZhi: this.diZhi[diZhiIndex],
            wuXing: {
                tianGan: this.wuXing[this.tianGan[tianGanIndex]],
                diZhi: this.wuXing[this.diZhi[diZhiIndex]]
            }
        };
    }

    // 获取时柱
    getHourPillar(hour, dayTianGan) {
        // 确定时辰
        let shiChenIndex = 0;
        for (let i = 0; i < this.shiChen.length; i++) {
            const sc = this.shiChen[i];
            if ((hour >= sc.start && hour < sc.end) ||
                (sc.start > sc.end && (hour >= sc.start || hour < sc.end))) {
                shiChenIndex = i;
                break;
            }
        }

        // 根据日干推时干
        const dayTianGanIndex = this.tianGan.indexOf(dayTianGan);
        let hourTianGanIndex;

        if (dayTianGanIndex === 0 || dayTianGanIndex === 5) { // 甲己日
            hourTianGanIndex = shiChenIndex % 10;
        } else if (dayTianGanIndex === 1 || dayTianGanIndex === 6) { // 乙庚日
            hourTianGanIndex = (shiChenIndex + 2) % 10;
        } else if (dayTianGanIndex === 2 || dayTianGanIndex === 7) { // 丙辛日
            hourTianGanIndex = (shiChenIndex + 4) % 10;
        } else if (dayTianGanIndex === 3 || dayTianGanIndex === 8) { // 丁壬日
            hourTianGanIndex = (shiChenIndex + 6) % 10;
        } else { // 戊癸日
            hourTianGanIndex = (shiChenIndex + 8) % 10;
        }

        return {
            tianGan: this.tianGan[hourTianGanIndex],
            diZhi: this.diZhi[shiChenIndex],
            wuXing: {
                tianGan: this.wuXing[this.tianGan[hourTianGanIndex]],
                diZhi: this.wuXing[this.diZhi[shiChenIndex]]
            }
        };
    }

    // 分析五行强弱
    analyzeWuXingStrength(bazi) {
        const wuXingCount = { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 };

        // 统计五行数量
        [bazi.year, bazi.month, bazi.day, bazi.hour].forEach(pillar => {
            wuXingCount[pillar.wuXing.tianGan]++;
            wuXingCount[pillar.wuXing.diZhi]++;
        });

        // 找出最强和最弱的五行
        let strongest = { element: '', count: 0 };
        let weakest = { element: '', count: 8 };

        for (const [element, count] of Object.entries(wuXingCount)) {
            if (count > strongest.count) {
                strongest = { element, count };
            }
            if (count < weakest.count) {
                weakest = { element, count };
            }
        }

        return {
            counts: wuXingCount,
            strongest,
            weakest,
            balance: this.calculateBalance(wuXingCount)
        };
    }

    // 计算五行平衡度
    calculateBalance(wuXingCount) {
        const total = Object.values(wuXingCount).reduce((sum, count) => sum + count, 0);
        const average = total / 5;
        const variance = Object.values(wuXingCount)
            .reduce((sum, count) => sum + Math.pow(count - average, 2), 0) / 5;

        // 平衡度评分（0-100）
        const balanceScore = Math.max(0, 100 - variance * 10);

        return {
            score: Math.round(balanceScore),
            level: balanceScore > 80 ? '很平衡' :
                   balanceScore > 60 ? '较平衡' :
                   balanceScore > 40 ? '一般' : '不平衡'
        };
    }

    // 分析喜忌神
    analyzeXiJi(bazi, wuXingAnalysis) {
        const dayMaster = bazi.day.wuXing.tianGan;
        const { strongest, weakest } = wuXingAnalysis;

        let xiShen = []; // 喜神
        let jiShen = []; // 忌神

        // 简化的喜忌分析
        if (strongest.element === dayMaster) {
            // 日主偏强，需要克制
            jiShen.push(dayMaster);
            xiShen.push(this.getKeElement(dayMaster));
            xiShen.push(this.getXieElement(dayMaster));
        } else {
            // 日主偏弱，需要扶助
            xiShen.push(dayMaster);
            xiShen.push(this.getShengElement(dayMaster));
            jiShen.push(this.getKeElement(dayMaster));
        }

        return { xiShen, jiShen };
    }

    // 获取相克的五行
    getKeElement(element) {
        const keMap = {
            '金': '木', '木': '土', '水': '火', '火': '金', '土': '水'
        };
        return keMap[element];
    }

    // 获取相生的五行
    getShengElement(element) {
        const shengMap = {
            '金': '水', '木': '火', '水': '木', '火': '土', '土': '金'
        };
        return shengMap[element];
    }

    // 获取泄气的五行
    getXieElement(element) {
        const xieMap = {
            '金': '水', '木': '火', '水': '木', '火': '土', '土': '金'
        };
        return xieMap[element];
    }

    // 生成完整分析报告
    generateFullAnalysis(birthDate, birthTime, name, gender, calendarType = 'solar') {
        // 如果是阴历，需要转换为阳历
        let actualBirthDate = birthDate;
        if (calendarType === 'lunar') {
            actualBirthDate = this.convertLunarToSolar(birthDate);
        }

        const bazi = this.calculateBazi(actualBirthDate, birthTime);
        const wuXingAnalysis = this.analyzeWuXingStrength(bazi);
        const xiJiAnalysis = this.analyzeXiJi(bazi, wuXingAnalysis);

        return {
            basicInfo: { name, gender, birthDate: actualBirthDate, birthTime, calendarType },
            bazi,
            wuXingAnalysis,
            xiJiAnalysis,
            detailedAnalysis: this.generateDetailedAnalysis(bazi, wuXingAnalysis, xiJiAnalysis, gender)
        };
    }

    // 简化的阴历转阳历（实际应该使用专业的历法转换库）
    convertLunarToSolar(lunarDate) {
        // 这里只是一个简化的示例，实际应该使用准确的农历转换算法
        const date = new Date(lunarDate);
        // 简单地加上大约18-50天的差值（农历通常比阳历晚）
        const solarDate = new Date(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        return solarDate.toISOString().split('T')[0];
    }

    // 生成详细分析
    generateDetailedAnalysis(bazi, wuXingAnalysis, xiJiAnalysis, gender) {
        const dayMaster = bazi.day.wuXing.tianGan;
        const balance = wuXingAnalysis.balance;

        return {
            overview: `命主日元为${dayMaster}，五行${balance.level}。${this.getElementCharacteristic(dayMaster)}`,
            strength: this.analyzeStrength(dayMaster, wuXingAnalysis),
            preference: `喜用神：${xiJiAnalysis.xiShen.join('、')}；忌神：${xiJiAnalysis.jiShen.join('、')}`,
            personality: this.analyzePersonality(dayMaster, bazi),
            health: this.analyzeHealth(wuXingAnalysis),
            family: this.analyzeFamily(bazi, gender),
            marriage: this.analyzeMarriage(bazi, gender),
            wealth: this.analyzeWealth(bazi),
            advice: this.generateAdvice(xiJiAnalysis, wuXingAnalysis)
        };
    }

    // 获取五行特征
    getElementCharacteristic(element) {
        const characteristics = {
            '金': '性格坚毅，有决断力，重义气',
            '木': '性格温和，有仁慈心，富有创造力',
            '水': '性格聪明，善变通，有智慧',
            '火': '性格热情，有礼貌，善表达',
            '土': '性格稳重，有信用，踏实可靠'
        };
        return characteristics[element] || '';
    }

    // 分析日主强弱
    analyzeStrength(dayMaster, wuXingAnalysis) {
        const count = wuXingAnalysis.counts[dayMaster];
        if (count >= 3) {
            return '日主偏强，自立能力强，有主见，不易受他人影响。';
        } else if (count <= 1) {
            return '日主偏弱，需要他人扶助，宜团队合作，不宜独自创业。';
        } else {
            return '日主中和，能力平衡，适应性强，发展稳定。';
        }
    }

    // 分析性格特征
    analyzePersonality(dayMaster, bazi) {
        const basePersonality = this.getElementCharacteristic(dayMaster);
        const monthElement = bazi.month.wuXing.tianGan;

        let personality = basePersonality;
        if (monthElement !== dayMaster) {
            personality += `，受${monthElement}影响，${this.getElementCharacteristic(monthElement)}`;
        }

        return personality;
    }

    // 分析健康状况
    analyzeHealth(wuXingAnalysis) {
        const { weakest } = wuXingAnalysis;
        const healthMap = {
            '金': '注意呼吸系统和皮肤健康',
            '木': '注意肝胆和筋骨健康',
            '水': '注意肾脏和泌尿系统健康',
            '火': '注意心血管和小肠健康',
            '土': '注意脾胃和消化系统健康'
        };

        return `整体健康状况良好，${healthMap[weakest.element]}。`;
    }

    // 分析六亲关系
    analyzeFamily(bazi, gender) {
        // 简化分析
        return '与父母关系和睦，兄弟姐妹互助，家庭关系和谐。';
    }

    // 分析婚姻感情
    analyzeMarriage(bazi, gender) {
        // 简化分析
        return '感情运势较好，适合晚婚，婚后生活幸福美满。';
    }

    // 分析财富运势
    analyzeWealth(bazi) {
        // 简化分析
        return '财运平稳，适合稳健投资，不宜投机取巧。';
    }

    // 生成人生建议
    generateAdvice(xiJiAnalysis, wuXingAnalysis) {
        const xiShen = xiJiAnalysis.xiShen[0];
        const advice = {
            '金': '宜从事金融、机械、汽车等行业，多佩戴金属饰品',
            '木': '宜从事教育、文化、园林等行业，多接触绿色植物',
            '水': '宜从事贸易、运输、水利等行业，多到水边走动',
            '火': '宜从事能源、电子、娱乐等行业，多晒太阳',
            '土': '宜从事房地产、农业、建筑等行业，多接触土地'
        };

        return `根据喜用神分析，${advice[xiShen] || '保持积极心态，多学习充实自己'}。注意身体健康，保持良好的生活习惯。`;
    }
}

// 全局实例
window.baziCalculator = new BaziCalculator();
